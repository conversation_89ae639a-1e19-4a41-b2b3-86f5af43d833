import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/add_image_option/add_image_option_screen.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/upload_post_files_in_background/upload_post_files.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/mention_parser/mention_parser.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';

enum EditPostScreenState { Loading, Success, Failed, Empty}

class EditPostBloc {
  //region Common variable
  late BuildContext context;
  final PostDetail postDetail;
  List<File> selectedImage = [];
  String reviewCount = "0";
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel ;
  //endregion

//region Text Editing Controller
  MyReachTextController addPostTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(r"@[a-zA-Z0-9_/\-]+"):
        AppTextStyle.contentText0(textColor: AppColors.brandGreen),
  }, onMatch: (List<String> match) {});
//endregion

//region Controller
  final editUpdatePostScreenStateCtrl = StreamController<EditPostScreenState>.broadcast();
  // final selectedImageCtrl = StreamController<List<PostImages>>.broadcast();
  final refreshCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  EditPostBloc(this.context, this.postDetail);
  //endregion
//region Init
  init(){

    //Add review count
    if(postDetail.commentType == CommentEnums.REVIEW.name){
      reviewCount = postDetail.ratingCount.toString();
      refreshCtrl.sink.add(true);
    }

    // Convert encoded mentions to display text for editing
    String displayText = MentionParser.convertToDisplayText(postDetail.text ?? '');
    addPostTextCtrl.text = displayText;

    // selectedImageCtrl.add(postDetail.postImages!);
    appConfigDataModel = Provider.of<AppConfigDataModel>(context, listen: false);


  }
//endregion








  void onTapAddImage() async{
    int imageLimit = appConfigDataModel.appConfig!.postImageLimit;

    //If web view then open download
    if(kIsWeb){
      return await CommonMethods.appDownloadDialog();
    }

    //Check if image count has reached the limit of 6
    if ((selectedImage.length + postDetail.images!.length ) >= imageLimit) {
      return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
    }


    Widget screen = const AddImageOptionScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // If value is null or postDetail is null or postImages is null
      if (value == null || postDetail == null || postDetail.images == null) {
        return;
      }

      try {
        // Check how many images are being added
        List<File> newImages = (value as List).cast<File>();
        int totalImagesAfterAddition = (selectedImage.length + postDetail.images!.length) + newImages.length;

        // If adding the new images exceeds the limit, show a warning and add only up to the limit
        if (totalImagesAfterAddition > imageLimit) {
          int imagesToAdd = imageLimit - (selectedImage.length + postDetail.images!.length); // Add only the number of images that fit
          // selectedImage.addAll(newImages.take(imagesToAdd).toList());
          selectedImage.insertAll(selectedImage.length, newImages.take(imagesToAdd).toList());

          // Refresh if needed (assuming refreshCtrl is a StreamController)
          refreshCtrl.sink.add(true);
          return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
        } else {
          // Add all selected images if within the limit
          selectedImage.addAll(newImages);
        }

        // Refresh if needed (assuming refreshCtrl is a StreamController)
        refreshCtrl.sink.add(true);

        // Print statement for debugging or confirmation
        //print('Images added successfully');
      } catch (e) {
        //print('Error while adding images: $e');
      }
    });
  }


  //region Remove local image
  void removeLocalImage({required String filePath }){
    //Remove image from the list
    selectedImage.removeWhere((element) => element.path == filePath);
    //Update ui
    refreshCtrl.sink.add(true);

  }
  //endregion

  ///Post
  //region Delete post image
  Future<void>deletePostImage({required String imageId,required String postReference })async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      await PostService().deletePostImage(imageId: imageId, postReference:postReference );

      //Remove image in local
      // postDetail.postImages.removeWhere((element) => element.postImageId == imageId);

      //Remove image from data model
      var post = postDataModel.allPostDetailList.firstWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      post.images!.removeWhere((element) => element.mediaId == imageId);

      //Refresh
      postDataModel.updateUi();
      //Update ui
      refreshCtrl.sink.add(true);


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion


  //region Edit post api call
  Future<void>editPostApiCall()async{
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);

    //Text field empty check
    //Text field empty check and image
    if(postDetail.images!.isEmpty && addPostTextCtrl.text.trim().isEmpty && selectedImage.isEmpty){
      return CommonMethods.toastMessage(AppStrings.emptyPostCanNotBeAdded, context);
    }
    try{
      //Loading
      editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Loading);
      //Add the flag that post is adding
      postDataModel.postingStatus = true;
      //Remove the exact product detail from main list
      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Close screen
      context.mounted?Navigator.pop(context):null;
      //Update ui
      postDataModel.updateUi();

      //Api call
      // Convert display text back to encoded format for API
      String encodedText = _convertDisplayTextToEncodedText(addPostTextCtrl.text);
      await PostAndCommentUploadFiles().editPostApiCall(
        postText: cleanText(encodedText),
            selectedImage: selectedImage.map((e) => e.path).toList(), postReference: postDetail.postOrCommentReference!
      );
    }
    on ApiErrorResponseMessage catch(error){
      //Add the flag that post is adding
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Add the flag that post is adding
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
  //endregion


  //region Clean text
  String cleanText(String input) {
    // Trim spaces and newlines from start and end
    String sanitized = input.trim();

    // Check if the resulting text starts and ends with non-space characters
    if (sanitized.isNotEmpty &&
        sanitized[0] != ' ' &&
        sanitized[sanitized.length - 1] != ' ') {
      return sanitized;
    }

    // Return an empty string if conditions are not met
    return '';
  }
  //endregion

  //region Convert display text to encoded text
  String _convertDisplayTextToEncodedText(String displayText) {
    // For edit post, we need to preserve the original encoded format
    // Since we don't track mentions in edit mode, we'll use the original text
    // and only replace if the display text has changed

    String originalDisplayText = MentionParser.convertToDisplayText(postDetail.text ?? '');

    // If the text hasn't changed, return the original encoded text
    if (displayText == originalDisplayText) {
      return postDetail.text ?? '';
    }

    // If text has changed, return the display text as-is
    // Note: This means edited mentions will lose their encoding
    // For full mention support in edit mode, we'd need to implement
    // the same mention tracking as in AddPostBloc
    return displayText;
  }
  //endregion


  //region Get single post
  Future<void >getSinglePost({required String postReference})async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      PostDetail postDetail = await PostService().getSinglePost(postReference: postReference);
      //Add updated data into data model
      postDataModel.addPostIntoList(postList: [postDetail]);
      //Add the flag that post is false
      postDataModel.postingStatus = false;
      //Update ui
      postDataModel.updateUi();


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      return;
    }
  }
//endregion


///Comment

  //region Edit comment api call
  Future<void>editCommentApiCall()async{
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);

    //Text field empty check
    //Text field empty check and image
    if(postDetail.images!.isEmpty && addPostTextCtrl.text.trim().isEmpty && selectedImage.isEmpty){
      return CommonMethods.toastMessage(AppStrings.emptyCommentCanNotBeAdded, context);
    }
    try{
      // Loading
      // editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Loading);
      // //Add the flag that post is adding
      // postDataModel.postingStatus = true;
      //Remove the exact product detail from main list
      // postDataModel.allPostDetailList.removeWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      //Close screen
      context.mounted?Navigator.pop(context):null;
      //Update ui
      postDataModel.updateUi();

      //Api call
      // Convert display text back to encoded format for API
      String encodedText = _convertDisplayTextToEncodedText(addPostTextCtrl.text);
      await PostAndCommentUploadFiles().editCommentApiCall(
        reviewCount:postDetail.commentType == CommentEnums.REVIEW.name?reviewCount:null,
          postText: encodedText,
          selectedImage: selectedImage.map((e) => e.path).toList(), commentReference: postDetail.postOrCommentReference!
      );
    }
    on ApiErrorResponseMessage catch(error){
      //Add the flag that post is adding
      // postDataModel.postingStatus = false;
      // //Update ui
      // postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      //Add the flag that post is adding
      // postDataModel.postingStatus = false;
      // //Update ui
      // postDataModel.updateUi();
      //Failed
      context.mounted?editUpdatePostScreenStateCtrl.sink.add(EditPostScreenState.Failed):null;
      //Message
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, AppConstants.userStoreCommonBottomNavigationContext);
      return;
    }
  }
  //endregion

  //region Delete comment image
  Future<void>deleteCommentImage({required String imageId,required String postOrCommentReference })async{
    try{
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Api call
      await PostService().deleteCommentImage(imageId: imageId, postReference:postOrCommentReference );

      //Remove image in local
      // postDetail.postImages.removeWhere((element) => element.postImageId == imageId);

      //Remove image from data model
      var post = postDataModel.allPostDetailList.firstWhere((element) => element.postOrCommentReference == postDetail.postOrCommentReference);
      post.images!.removeWhere((element) => element.mediaId == imageId);

      //Refresh
      postDataModel.updateUi();
      //Update ui
      refreshCtrl.sink.add(true);


    }
    on ApiErrorResponseMessage catch(error){
      context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
      return;
    }
    catch(error){
      context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion


//region Dispose
  void dispose(){
    editUpdatePostScreenStateCtrl.close();
    refreshCtrl.close();
  }
//endregion
}